[project]
name = "flappybird"
version = "0.1.0"
requires-python = ">=3.11"
dependencies = [
    "flappy-bird-gymnasium>=0.4.0",
    "gymnasium[mujoco]>=0.29.1",
    "huggingface-sb3>=3.0",
    "marimo>=0.18.3",
    "moviepy>=2.2.1",
    "opencv-python>=********",
    "rich>=14.2.0",
    "richer>=0.1.6",
    "seaborn>=0.13.2",
    "stable-baselines3[extra]>=2.7.0",
    "torch>=2.9.0",
    "tqdm>=4.67.1",
    "tyro>=0.9.35",
    "wandb>=0.23.1",
]

[project.optional-dependencies]
tests = ["pytest>=9.0.0"]

[tool.pytest.ini_options]
pythonpath = "."
testpaths = ["tests"]

[tool.ruff]
line-length = 100 # Increase from default 88

[dependency-groups]
dev = [
    "ipython>=9.8.0",
]
