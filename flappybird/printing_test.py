import random

from rich.console import Console
from tqdm import tqdm

console = Console()


def train_with_progress(n_episodes, log_interval=1000):
    """Train with Rich Progress + tqdm."""

    # table = Table(title="Training Progress", show_header=True)
    # table.add_column("Episode", justify="right", style="cyan")
    # table.add_column("Reward Sum", justify="right", style="magenta")
    # table.add_column("Loss", justify="right", style="yellow")
    # table.add_column("Entropy", justify="right", style="green")
    # table.add_column("LR", justify="right", style="blue")
    # console.print(table)

    tqdm.write(
        f"{'Episode':>8s} | {'Reward':>8s} | {'Loss (P)':>8s} | {'Loss (V)':>8s} | "
        f"{'Entropy':>9s} | {'LR':>8s}"
    )
    for i_episode in tqdm(range(n_episodes), desc="Training", unit="ep"):
        # time.sleep(0.0001)

        if (i_episode + 1) % log_interval == 0:
            tqdm.write(
                f"{i_episode + 1:> 8d} | {100 * random.random():> 8.1f} | "
                f"{random.random():> 8.2f} | {random.random():> 8.2f} | "
                f"{random.random():> .2e} | {random.random():> .2e}"
            )


train_with_progress(100_000)
