import marimo

__generated_with = "0.18.3"
app = marimo.App(width="medium")

with app.setup:
    import random

    import matplotlib.pyplot as plt
    import numpy as np
    import pandas as pd
    import seaborn as sns
    import torch
    import torch.nn as nn
    from torch.distributions.normal import Normal

    import gymnasium as gym


    plt.rcParams["figure.figsize"] = (10, 5)


@app.cell
def _():
    class Policy_Network(nn.Module):
        """Parametrized Policy Network."""

        def __init__(self, obs_space_dims: int, action_space_dims: int):
            """Initializes a neural network that estimates the mean and standard deviation
             of a normal distribution from which an action is sampled from.

            Args:
                obs_space_dims: Dimension of the observation space
                action_space_dims: Dimension of the action space
            """
            super().__init__()

            hidden_space1 = 16  # Nothing special with 16, feel free to change
            hidden_space2 = 32  # Nothing special with 32, feel free to change

            # Shared Network
            self.shared_net = nn.Sequential(
                nn.Linear(obs_space_dims, hidden_space1),
                nn.<PERSON>(),
                nn.Linear(hidden_space1, hidden_space2),
                nn.Tanh(),
            )

            # Policy Mean specific Linear Layer
            self.policy_mean_net = nn.Sequential(
                nn.Linear(hidden_space2, action_space_dims)
            )

            # Policy Std Dev specific Linear Layer
            self.policy_stddev_net = nn.Sequential(
                nn.Linear(hidden_space2, action_space_dims)
            )

        def forward(self, x: torch.Tensor) -> tuple[torch.Tensor, torch.Tensor]:
            """Conditioned on the observation, returns the mean and standard deviation
             of a normal distribution from which an action is sampled from.

            Args:
                x: Observation from the environment

            Returns:
                action_means: predicted mean of the normal distribution
                action_stddevs: predicted standard deviation of the normal distribution
            """
            shared_features = self.shared_net(x.float())

            action_means = self.policy_mean_net(shared_features)
            action_stddevs = torch.log(
                1 + torch.exp(self.policy_stddev_net(shared_features))
            )

            return action_means, action_stddevs

    class REINFORCE:
        """REINFORCE algorithm."""

        def __init__(self, obs_space_dims: int, action_space_dims: int):
            """Initializes an agent that learns a policy via REINFORCE algorithm [1]
            to solve the task at hand (Inverted Pendulum v4).

            Args:
                obs_space_dims: Dimension of the observation space
                action_space_dims: Dimension of the action space
            """

            # Hyperparameters
            self.learning_rate = 1e-4  # Learning rate for policy optimization
            self.gamma = 0.99  # Discount factor
            self.eps = 1e-6  # small number for mathematical stability

            self.probs = []  # Stores probability values of the sampled action
            self.rewards = []  # Stores the corresponding rewards

            self.net = Policy_Network(obs_space_dims, action_space_dims)
            self.optimizer = torch.optim.AdamW(self.net.parameters(), lr=self.learning_rate)

        def sample_action(self, state: np.ndarray) -> float:
            """Returns an action, conditioned on the policy and observation.

            Args:
                state: Observation from the environment

            Returns:
                action: Action to be performed
            """
            state = torch.tensor(np.array([state]))
            action_means, action_stddevs = self.net(state)

            # create a normal distribution from the predicted
            #   mean and standard deviation and sample an action
            distrib = Normal(action_means[0] + self.eps, action_stddevs[0] + self.eps)
            action = distrib.sample()
            prob = distrib.log_prob(action)

            action = action.numpy()

            self.probs.append(prob)

            return action

        def update(self):
            """Updates the policy network's weights."""
            running_g = 0
            gs = []

            # Discounted return (backwards) - [::-1] will return an array in reverse
            for R in self.rewards[::-1]:
                running_g = R + self.gamma * running_g
                gs.insert(0, running_g)

            deltas = torch.tensor(gs)

            log_probs = torch.stack(self.probs).squeeze()

            # Update the loss with the mean log probability and deltas
            # Now, we compute the correct total loss by taking the sum of the element-wise products.
            loss = -torch.sum(log_probs * deltas)

            # Update the policy network
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()

            # Empty / zero out all episode-centric/related variables
            self.probs = []
            self.rewards = []
    return (REINFORCE,)


@app.cell
def _(REINFORCE):
    # Create and wrap the environment
    env = gym.make("InvertedPendulum-v4")
    wrapped_env = gym.wrappers.RecordEpisodeStatistics(env, 50)  # Records episode-reward

    total_num_episodes = int(5e3)  # Total number of episodes
    # Observation-space of InvertedPendulum-v4 (4)
    obs_space_dims = env.observation_space.shape[0]
    # Action-space of InvertedPendulum-v4 (1)
    action_space_dims = env.action_space.shape[0]
    rewards_over_seeds = []

    def train():
        # Reinitialize agent every seed
        agent = REINFORCE(obs_space_dims, action_space_dims)
        reward_over_episodes = []

        for episode in range(total_num_episodes):
            # gymnasium v26 requires users to set seed while resetting the environment
            obs, info = wrapped_env.reset(seed=seed)

            done = False
            while not done:
                action = agent.sample_action(obs)
                obs, reward, terminated, truncated, info = wrapped_env.step(action)
                agent.rewards.append(reward)
                done = terminated or truncated

            reward_over_episodes.append(wrapped_env.return_queue[-1])
            agent.update()

            if episode % 1000 == 0:
                avg_reward = int(np.mean(wrapped_env.return_queue))
                print("Episode:", episode, "Average Reward:", avg_reward)

        rewards_over_seeds.append(reward_over_episodes)

        return agent


    agent_store = {}
    for seed in [1, 2, 3, 5, 8]:  # Fibonacci seeds
        # set seed
        torch.manual_seed(seed)
        random.seed(seed)
        np.random.seed(seed)
    
        agent = train()
        agent_store[seed] = agent
    return agent_store, rewards_over_seeds


@app.cell
def _(rewards_over_seeds):
    def reward_curve():
        """Create reward curve showing average reward over seeds with STD bounds."""
    
        df1 = pd.DataFrame(rewards_over_seeds).melt()
        df1.rename(columns={"variable": "episodes", "value": "reward"}, inplace=True)
        # df1["reward"] = df1["reward"].apply(lambda x: x.item())
    
        fig, ax = plt.subplots(figsize=(8, 4))
        sns.set(style="darkgrid", context="talk", palette="rainbow")
        sns.lineplot(x="episodes", y="reward", data=df1).set(
            title="REINFORCE for InvertedPendulum-v4"
        )
        return fig

    reward_fig = reward_curve()
    plt.show()
    return (reward_fig,)


@app.cell
def _(reward_fig):
    reward_fig.savefig("notebooks/reward-reinforce-pendulum.png", bbox_inches="tight")
    return


@app.cell
def _(agent_store):
    from dataclasses import dataclass

    def boxplot_episode_rewards(episode_rewards):
        """Create a boxplot of episode rewards."""
        fig, ax = plt.subplots(figsize=(4, 6))
        sns.boxplot(y=episode_rewards, ax=ax)
        ax.set_ylabel("Episode Reward")
        ax.set_title("Distribution of Episode Rewards")
        return fig

    @dataclass
    class EvalConfig:
        n_episodes: int = 20
        seed: int = 0
        seed_fixed: bool = False

    def eval(agent):
        # Create separate evalution environment
        env = gym.make("InvertedPendulum-v4", render_mode="rgb_array")
        env = gym.wrappers.RecordEpisodeStatistics(env)
        env = gym.wrappers.RecordVideo(env, video_folder="notebooks/video/eval", episode_trigger=lambda e: True)
        cfg = EvalConfig()
    
        with torch.no_grad():
            episode_rewards = []
            for episode in range(cfg.n_episodes):
                # Each episode has predictable seed for reproducible evaluation
                # making sure policy can cope with env stochasticity
                seed = cfg.seed if cfg.seed_fixed else cfg.seed + episode
                state, _ = env.reset(seed=seed)
                done = False
                while not done:
                    action = agent.sample_action(state)
                    state, reward, terminated, truncated, info = env.step(action)
                    done = terminated or truncated
    
                # Extract episode statistics from info (available after episode ends)
                if "episode" in info:
                    episode_reward = info["episode"]["r"]
                    episode_length = info["episode"]["l"]
                    episode_rewards.append(episode_reward)
                    print(
                        f"Episode {episode:> 3d} | "
                        f"Reward: {episode_reward:> 6.2f} | Length: {episode_length:> 4d}"
                    )
    
            if episode_rewards:
                mean_reward = np.mean(episode_rewards)
                std_reward = np.std(episode_rewards)
                print(
                    f"\nMean reward over {len(episode_rewards)} episodes: {mean_reward:.2f} +/- {std_reward:.2f}"
                )
    
                # Create and log boxplot of episode rewards
                fig = boxplot_episode_rewards(episode_rewards)
    
        env.close()
        return fig

    boxplot_figs = []
    for s, agnt in agent_store.items():
        print(f"Evaluating agent trained with seed={s} ...")
        fig = eval(agnt)
        boxplot_figs.append(fig)
    return


if __name__ == "__main__":
    app.run()
