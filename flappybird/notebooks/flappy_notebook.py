import marimo

__generated_with = "0.18.3"
app = marimo.App(width="medium")


@app.cell
def _():
    import gymnasium as gym
    import flappy_bird_gymnasium
    from typing import Callable
    return Callable, gym


@app.cell
def _(Callable, gym):
    def make_env(
        env_id,
        render_mode="rgb_array",
        record_stats=False,
        max_episode_steps: int | None = 10_000,
        stack_size: int | None = None,
        video_folder: str | None = None,
        episode_trigger: Callable[[int], bool] | None = None,
        **kwargs,
    ):
        """Make the environment."""
        env = gym.make(
            env_id, render_mode=render_mode, max_episode_steps=max_episode_steps, **kwargs
        )
        if record_stats:
            env = gym.wrappers.RecordEpisodeStatistics(env, buffer_length=100)
        if video_folder:
            if episode_trigger is None:
                print("No episode trigger provided.")
            env = gym.wrappers.RecordVideo(
                env,
                video_folder,
                episode_trigger=episode_trigger,
                disable_logger=True,
            )
        if stack_size:
            env = gym.wrappers.FrameStack(env, num_stack=stack_size)
        return env
    return (make_env,)


@app.cell
def _(make_env):
    import random

    env = make_env("FlappyBird-v0", 
                   record_stats=True, 
                   video_folder="some/folder",
                  )
    env.reset(seed=42)
    # env.set_wrapper_attr("disable_logger", True)
    env.set_wrapper_attr("video_folder", "some/new/changed/folder")
    env.get_wrapper_attr("video_folder")
    for e in range(100):
        episode_done = False
        env.reset()
        while not episode_done:
            action = random.randint(0, 2)
            state, reward, terminated, truncated, _ = env.step(action)
            episode_done = terminated or truncated
    env.episode_count

    return


@app.cell
def _():
    import scipy
    import numpy as np
    import timeit
    from collections import deque

    def discount_cumsum(x, discount):
        """
        magic from rllab for computing discounted cumulative sums of vectors.

        input: 
            vector x, 
            [x0, 
             x1, 
             x2]

        output:
            [x0 + discount * x1 + discount^2 * x2,  
             x1 + discount * x2,
             x2]
        """
        return scipy.signal.lfilter([1], [1, float(-discount)], x[::-1], axis=0)[::-1]

    def compute_returns(rewards, gamma, normalize=False, device="cuda"):
        """Compute the returns from the rewards.

        The discounted returns at each timestep are calculated as:
            G_t = r_(t+1) + gamma*G_(t+1)

        This follows a dynamic programming approach, computing from the last timestep to the first
        to avoid redundant computations.

        Args:
            rewards (list): List of rewards
            gamma (float): Discount factor
            normalize (bool): Whether to normalize the returns
            device (str): Device to use for computation

        Returns:
            returns (torch.Tensor): Tensor of returns
            mean (float): Mean of the returns (if normalize=True)
            std (float): Standard deviation of the returns (if normalize=True)
        """

        if gamma <= 0.0 or gamma > 1.0:
            raise ValueError(f"Invalid gamma: {gamma}. Should be in range (0, 1].")

        returns = deque(maxlen=len(rewards))
        discounted_return = 0
        for r in reversed(rewards):
            discounted_return = r + gamma * discounted_return
            # Using deque to prepend in O(1) to keep the returns in chronological order.
            returns.appendleft(discounted_return)

        # Standardize the returns to make the training more stable
        # returns = torch.tensor(returns, device=device, dtype=torch.float32)
        returns = np.array(returns)
        if normalize:
            eps = np.finfo(np.float32).eps.item()  # Guard against division by zero (std=0)
            mean, std = returns.mean(), returns.std()
            returns = (returns - mean) / (std + eps)
            return returns, mean, std

        return returns


    rewards = np.random.randn(50)
    gamma = 0.9
    # np.allclose(
    #     discount_cumsum(rewards, gamma),
    #     compute_returns(rewards, gamma))

    n = 10_000
    t0 = timeit.timeit('discount_cumsum(rewards, gamma)', globals=globals(), number=n)
    t1 = timeit.timeit('compute_returns(rewards, gamma)', globals=globals(), number=n)
    print(f"discount_cumsum is {t1/t0:.2f}x faster!")
    return


@app.cell
def _():
    from tqdm import tqdm

    def _log_stats(i_episode, reward_stats, length_stats, score_stats, bmr=5.0):
        def _stat_line(name, stats):
            return (
                f"{name:8s}: {stats['mean']: >6.2f} +/- {stats['std']:.2f}"
                f"  ({stats['min']:.2f} <-> {stats['max']:.2f})"
            )
        tqdm.write(
            f"\nEvaluation: [mean +/- std (min <-> max)]\n"
            f"{_stat_line('Reward', reward_stats)}\n"
            f"{_stat_line('Length', length_stats)}\n"
            f"{_stat_line('Score', score_stats)}\n"
            f"{'Best':8s}: {bmr:.2f}\n"
        )

    rs = {"mean": 52.0255, "std": 0.05, "min": -5.0, "max": 5.0}
    ls = {"mean": 205, "std": 0.05, "min": 50, "max": 210}
    ss = {"mean": 10, "std": 0.05, "min": 5, "max": 12}

    _log_stats(1000, rs, ls, ss)
    return


@app.cell(disabled=True)
def _(info, result):
    from rich.console import Console
    from rich.table import Table
    from tqdm import tqdm
    import time
    import random

    console = Console()

    def train_with_progress(n_episodes, log_interval=250):
        """Train with Rich Progress + tqdm."""

        table = Table(title="Training Progress", show_header=True)
        table.add_column("Episode", justify="right", style="cyan")
        table.add_column("Reward Sum", justify="right", style="magenta")
        table.add_column("Loss", justify="right", style="yellow")
        table.add_column("Entropy", justify="right", style="green")
        table.add_column("LR", justify="right", style="blue")

        for i_episode in tqdm(range(n_episodes), desc="Training", unit="ep"):
            time.sleep(0.001)

            if (i_episode + 1) % log_interval == 0:
                tqdm.write(
                        f"{i_episode + 1:>8d} | {info['summed_reward']:>10.4f} | "
                        f"{result.loss:>10.4f} | {result.entropy_term:>10.2e} | "
                        f"{result.last_lr:>10.2e}"
                    )
                # table.add_row(
                #     f"{i_episode + 1}",
                #     f"{random.random():.4f}",
                #     f"{random.random():.4f}",
                #     f"{random.random():.2e}",
                #     f"{random.random():.2e}",
                # )
                # console.print(table)

    train_with_progress(5_000)
    return


if __name__ == "__main__":
    app.run()
