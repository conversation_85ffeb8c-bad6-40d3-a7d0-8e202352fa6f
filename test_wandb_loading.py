#!/usr/bin/env python3
"""Test script to verify wandb model loading functionality."""

import os

import wandb

from flappybird.agents import load_model_with_wandb


def test_wandb_loading():
    """Test loading a model from wandb."""

    # Set up wandb authentication
    api_key_path = "/home/<USER>/reinforcement-learning/wandb_api_key.txt"
    if os.path.exists(api_key_path):
        with open(api_key_path, "r") as f:
            api_key = f.read().strip()
        wandb.login(key=api_key)
        print("✅ Wandb authentication successful")
    else:
        print(
            "❌ API key file not found. Please set WANDB_API_KEY environment variable."
        )
        return

    # Test the API connection
    try:
        api = wandb.Api()
        print("✅ Wandb API connection successful")

        # List some recent runs to help user identify a run_id to test with
        print("\nRecent runs in your account:")
        runs = api.runs(limit=5)  # Get last 5 runs
        for i, run in enumerate(runs):
            print(
                f"{i + 1}. {run.entity}/{run.project}/{run.id} - {run.name} ({run.state})"
            )

        print("\nTo test model loading, run:")
        print("python test_wandb_loading.py <entity/project/run_id>")

    except Exception as e:
        print(f"❌ Error connecting to wandb API: {e}")
        return


def test_model_loading(run_id):
    """Test loading a specific model.

    Args:
        run_id: W&B run ID in format "entity/project/run_id" (e.g., "jacobnzw-n-a/flappybird_vpg/abc123def")
    """
    try:
        print(f"\nTesting model loading from run: {run_id}")

        # Get the training run using the API
        api = wandb.Api()
        train_run = api.run(run_id)
        print(f"✅ Found run: {train_run.name} ({train_run.state})")

        # Try loading a policy model (most common)
        model = load_model_with_wandb(train_run, model_name="flappybird_vpg_policy")
        print(f"✅ Successfully loaded policy model: {type(model)}")
        print(f"   Model device: {next(model.parameters()).device}")
        print(f"   Model parameters: {sum(p.numel() for p in model.parameters())}")

    except Exception as e:
        print(f"❌ Error loading model: {e}")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        run_id = sys.argv[1]
        test_model_loading(run_id)
    else:
        test_wandb_loading()
