# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/
!.vscode/launch.json

# uv
.uv/

# Machine Learning / RL specific
*.pkl
*.pth
*.pt
*.h5
*.hdf5
models/
checkpoints/
logs/
tensorboard_logs/
wandb/
mlruns/

# Video files
# *.mp4
*.avi
*.mov
*.mkv

# Image files
# *.png
*.jpg
*.jpeg
*.gif
*.bmp

# Data files
*.csv
*.json
*.npy
*.npz
data/
datasets/

# ViZDoom specific
_vizdoom/
*.wad
*.cfg
*.ini
!_vizdoom.ini

# Training outputs
train_dir/
experiments/
results/
replay_buffer/

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# Hugging Face cache
.cache/
transformers_cache/
